import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { 
  Award, 
  Clock, 
  Users, 
  Heart,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "Personal Touch",
      description: "Every client gets my direct attention and customized solutions that fit their unique needs."
    },
    {
      icon: Users,
      title: "Client-Focused",
      description: "Your success is my success. I work closely with you to understand and solve your challenges."
    },
    {
      icon: Clock,
      title: "Responsive Service",
      description: "Quick response times and flexible scheduling because your technology can't wait."
    },
    {
      icon: Award,
      title: "Quality Work",
      description: "Years of experience delivering reliable solutions that stand the test of time."
    }
  ];

  const experience = [
    "10+ years in IT support and troubleshooting",
    "5+ years building custom websites and web applications",
    "Hundreds of satisfied customers and successful projects",
    "Expertise in both technical solutions and clear communication",
    "Local business owner who understands small business needs"
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="hero-section py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                Hi, I'm Your <span className="gradient-text">Technology Partner</span>
              </h1>
              <p className="text-lg text-muted-foreground mb-6">
                My name is [Your Name], and I'm passionate about helping individuals and small businesses 
                succeed with technology. Whether you're struggling with a computer issue or need a 
                professional website, I'm here to provide solutions that actually work.
              </p>
              <p className="text-lg text-muted-foreground mb-8">
                What sets me apart is my commitment to understanding your unique situation and providing 
                personalized support. No call centers, no generic responses – just direct, professional 
                service from someone who genuinely cares about solving your problems.
              </p>
              <Link to="/contact">
                <Button size="lg" className="bg-accent hover:bg-accent-light text-accent-foreground">
                  Let's Work Together
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
            <div className="flex justify-center">
              <div className="w-80 h-96 bg-gradient-to-br from-secondary to-muted rounded-2xl shadow-strong flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Users className="w-20 h-20 mx-auto mb-4 text-primary" />
                  <p className="text-lg font-medium">Professional Photo</p>
                  <p className="text-sm">Coming Soon</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="py-20 bg-secondary/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Experience You Can Trust
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              With years of hands-on experience and a track record of satisfied clients, 
              I bring the expertise you need to solve your technology challenges.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <Card className="service-card">
              <CardHeader>
                <CardTitle className="text-2xl text-center">My Background</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {experience.map((item, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-trust mt-0.5 flex-shrink-0" />
                    <span className="text-muted-foreground">{item}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <div>
              <h3 className="text-2xl font-semibold text-foreground mb-6">
                My Mission
              </h3>
              <p className="text-lg text-muted-foreground mb-6">
                Technology should help your life and business run smoothly, not create stress and frustration. 
                My mission is to bridge the gap between complex technology and real-world solutions that make sense.
              </p>
              <p className="text-lg text-muted-foreground mb-6">
                I started this business because I believe everyone deserves access to quality IT support 
                and professional web development services, regardless of the size of their business or 
                technical background.
              </p>
              <p className="text-lg text-muted-foreground">
                When you work with me, you're not just getting technical expertise – you're getting a 
                partner who is invested in your success and committed to providing solutions that work 
                for your specific situation.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              What Drives My Work
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              These core values guide every interaction and solution I provide for my clients.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-4">{value.title}</h3>
                <p className="text-muted-foreground">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Experience the Difference?
          </h2>
          <p className="text-xl text-primary-foreground/90 mb-8">
            Let's start with a conversation about your needs and how I can help you achieve your goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button size="lg" variant="secondary" className="text-lg px-8">
                Get in Touch
              </Button>
            </Link>
            <Link to="/services">
              <Button size="lg" variant="outline" className="text-lg px-8 border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10">
                View Services
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;