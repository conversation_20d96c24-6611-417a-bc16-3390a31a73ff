import { Link } from 'react-router-dom';
import { Monitor, Phone, Mail, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="flex items-center justify-center w-10 h-10 bg-primary-foreground/10 rounded-lg">
                <Monitor className="w-5 h-5" />
              </div>
              <span className="text-lg font-semibold">TechSupport Pro</span>
            </div>
            <p className="text-primary-foreground/80 text-sm">
              Personal IT support and web development services you can trust. 
              Professional solutions with a human touch.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2">
              <Link to="/about" className="block text-sm text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                About Us
              </Link>
              <Link to="/services" className="block text-sm text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Services
              </Link>
              <Link to="/contact" className="block text-sm text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Contact
              </Link>
              <Link to="/login" className="block text-sm text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Customer Login
              </Link>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold mb-4">Services</h3>
            <div className="space-y-2">
              <span className="block text-sm text-primary-foreground/80">IT Support</span>
              <span className="block text-sm text-primary-foreground/80">Web Development</span>
              <span className="block text-sm text-primary-foreground/80">Network Setup</span>
              <span className="block text-sm text-primary-foreground/80">Device Troubleshooting</span>
            </div>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-semibold mb-4">Get in Touch</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-primary-foreground/60" />
                <span className="text-sm text-primary-foreground/80">(*************</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-primary-foreground/60" />
                <span className="text-sm text-primary-foreground/80"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-primary-foreground/60" />
                <span className="text-sm text-primary-foreground/80">Local Service Area</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center">
          <p className="text-sm text-primary-foreground/60">
            © 2024 TechSupport Pro. All rights reserved. | Professional IT support with a personal touch.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;