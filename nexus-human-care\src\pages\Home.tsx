import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Monitor, 
  Globe, 
  Shield, 
  Users, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Phone,
  Mail
} from 'lucide-react';

const Home = () => {
  const services = [
    {
      icon: Monitor,
      title: "IT Support",
      description: "Hardware troubleshooting, software help, and network setup for homes and small businesses.",
      features: ["Device Setup", "Virus Removal", "Network Configuration", "Data Recovery"]
    },
    {
      icon: Globe,
      title: "Web Development",
      description: "Custom websites and web applications tailored to your business needs.",
      features: ["Custom Websites", "Site Maintenance", "SEO Optimization", "E-commerce Solutions"]
    }
  ];

  const benefits = [
    {
      icon: Users,
      title: "Personal Service",
      description: "Work directly with an experienced professional who understands your needs."
    },
    {
      icon: Clock,
      title: "Quick Response",
      description: "Fast response times and flexible scheduling that works around your schedule."
    },
    {
      icon: Shield,
      title: "Trusted & Reliable",
      description: "Years of experience helping individuals and small businesses with their tech needs."
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-secondary/50 to-primary/5 py-24 lg:py-40">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-small"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-l from-accent/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-3xl"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 bg-accent/10 border border-accent/20 rounded-full text-accent text-sm font-medium mb-8">
              <Shield className="w-4 h-4 mr-2" />
              Trusted Local IT & Web Professional
            </div>
            <h1 className="text-4xl md:text-7xl font-bold text-foreground mb-8 leading-tight">
              Personal IT & Web Support{' '}
              <span className="gradient-text">You Can Rely On</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Professional technology solutions with a personal touch. From daily IT support to custom web development, 
              I'm here to help you and your business succeed with technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link to="/contact">
                <Button size="lg" className="bg-accent hover:bg-accent-light text-accent-foreground text-lg px-10 py-4 shadow-strong hover:shadow-medium transition-all duration-300">
                  Get Support Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/services">
                <Button variant="outline" size="lg" className="text-lg px-10 py-4 border-2 hover:bg-secondary/50 transition-all duration-300">
                  View Services
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-24 bg-background border-t border-border/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              How I Can Help You
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Whether you need ongoing IT support or a custom website, I provide personalized solutions 
              that fit your specific needs and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="service-card hover:scale-105 transition-transform duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-xl">
                      <service.icon className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{service.title}</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-base">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-trust" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Me */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Why Work With Me?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Get the personalized attention and expertise your technology needs deserve.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-accent/10 rounded-2xl mx-auto mb-6">
                  <benefit.icon className="w-8 h-8 text-accent" />
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-4">{benefit.title}</h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-primary-foreground/90 mb-8">
            Let's discuss how I can help solve your technology challenges and support your business goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button size="lg" variant="secondary" className="text-lg px-8">
                <Phone className="mr-2 h-5 w-5" />
                Schedule a Call
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="text-lg px-8 border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10">
                <Mail className="mr-2 h-5 w-5" />
                Send a Message
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;