import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Link } from 'react-router-dom';
import { Lock, User, AlertCircle, ArrowRight } from 'lucide-react';

const Login = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate login attempt
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
  };

  const features = [
    "View your service history",
    "Track open support tickets",
    "Access important documents",
    "Schedule appointments",
    "Direct communication channel"
  ];

  return (
    <div className="min-h-screen pt-20">
      <section className="hero-section py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Login Form */}
            <Card className="service-card max-w-md mx-auto w-full">
              <CardHeader className="text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mx-auto mb-4">
                  <Lock className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="text-2xl">Customer Login</CardTitle>
                <CardDescription>
                  Access your personal support dashboard
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert className="mb-6 border-accent/20 bg-accent/5">
                  <AlertCircle className="h-4 w-4 text-accent" />
                  <AlertDescription className="text-accent">
                    <strong>Coming Soon:</strong> The customer portal will be available once we integrate 
                    with our backend system. You'll receive login credentials after your first service.
                  </AlertDescription>
                </Alert>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="<EMAIL>"
                      disabled
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      placeholder="••••••••"
                      disabled
                    />
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={true}
                  >
                    {isLoading ? "Signing In..." : "Sign In"}
                  </Button>
                  
                  <div className="text-center text-sm">
                    <a href="#" className="text-muted-foreground hover:text-primary transition-colors">
                      Forgot your password?
                    </a>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Information Section */}
            <div className="space-y-8">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                  Your Personal <span className="gradient-text">Support Hub</span>
                </h1>
                <p className="text-xl text-muted-foreground mb-8">
                  Once available, the customer portal will give you complete visibility into your 
                  IT support history and ongoing projects, making it easier than ever to stay 
                  connected and informed.
                </p>
              </div>

              <Card className="service-card">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5 text-primary" />
                    <span>What You'll Get Access To</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0"></div>
                        <span className="text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="bg-gradient-to-r from-primary/5 to-accent/5 p-6 rounded-xl border border-border/50">
                <h3 className="font-semibold text-foreground mb-2">Ready to Get Started?</h3>
                <p className="text-muted-foreground mb-4">
                  Don't have an account yet? Contact me to discuss your needs and get set up 
                  with personalized support.
                </p>
                <Link to="/contact">
                  <Button className="bg-accent hover:bg-accent-light text-accent-foreground">
                    Contact Me Today
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Preview */}
      <section className="py-20 bg-secondary/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            Future Customer Dashboard Preview
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Here's a glimpse of what the customer portal will look like once it's integrated 
            with our backend systems.
          </p>
          
          <div className="bg-gradient-to-br from-muted/50 to-secondary/80 rounded-xl p-8 border border-border/50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-xl font-bold text-primary">3</span>
                </div>
                <p className="font-semibold text-foreground">Active Tickets</p>
                <p className="text-sm text-muted-foreground">Support requests in progress</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-trust/10 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-xl font-bold text-trust">12</span>
                </div>
                <p className="font-semibold text-foreground">Completed</p>
                <p className="text-sm text-muted-foreground">Successfully resolved issues</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-accent/10 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <span className="text-xl font-bold text-accent">2</span>
                </div>
                <p className="font-semibold text-foreground">Projects</p>
                <p className="text-sm text-muted-foreground">Web development in progress</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Login;