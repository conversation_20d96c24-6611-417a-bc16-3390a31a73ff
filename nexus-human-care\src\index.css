@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional IT Support & Web Development Design System */

@layer base {
  :root {
    /* Brand Colors - Warm and Professional */
    --background: 0 0% 100%;
    --foreground: 222 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 222 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 20% 15%;

    /* Primary - Professional Blue */
    --primary: 215 28% 17%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 215 28% 25%;
    --primary-lighter: 215 28% 35%;

    /* Secondary - Warm Gray */
    --secondary: 220 10% 96%;
    --secondary-foreground: 222 20% 25%;

    /* Muted Tones */
    --muted: 220 10% 96%;
    --muted-foreground: 220 8% 46%;

    /* Accent - Warm Orange for CTAs */
    --accent: 32 95% 44%;
    --accent-foreground: 0 0% 98%;
    --accent-light: 32 95% 52%;
    --accent-lighter: 32 95% 60%;

    /* Trust Green for Success States */
    --trust: 142 69% 58%;
    --trust-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 215 28% 17%;

    --radius: 0.75rem;

    /* Custom Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--accent-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--secondary)) 100%);

    /* Professional Shadows */
    --shadow-soft: 0 2px 10px -3px hsl(var(--foreground) / 0.1);
    --shadow-medium: 0 4px 20px -4px hsl(var(--foreground) / 0.15);
    --shadow-strong: 0 8px 30px -6px hsl(var(--foreground) / 0.2);

    /* Smooth Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary-lighter bg-clip-text text-transparent;
  }
}

@layer components {
  .hero-section {
    @apply bg-gradient-to-br from-background via-secondary/30 to-background;
  }

  .service-card {
    @apply bg-card rounded-xl border border-border/50 p-6 transition-all duration-300;
    box-shadow: 0 2px 10px -3px hsl(var(--foreground) / 0.1);
  }

  .service-card:hover {
    box-shadow: 0 4px 20px -4px hsl(var(--foreground) / 0.15);
  }

  .professional-button {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }
}

@layer utilities {
  .shadow-soft {
    box-shadow: 0 2px 10px -3px hsl(var(--foreground) / 0.1);
  }

  .shadow-medium {
    box-shadow: 0 4px 20px -4px hsl(var(--foreground) / 0.15);
  }

  .shadow-strong {
    box-shadow: 0 8px 30px -6px hsl(var(--foreground) / 0.2);
  }
}