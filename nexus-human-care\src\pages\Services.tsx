import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON> } from 'react-router-dom';
import { 
  Monitor, 
  Globe, 
  Wifi, 
  HardDrive,
  Shield,
  Smartphone,
  Palette,
  Search,
  ShoppingCart,
  Settings,
  ArrowRight,
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';

const Services = () => {
  const itServices = [
    {
      icon: Monitor,
      title: "Computer Troubleshooting",
      description: "Hardware and software issues, slow performance, crashes, and system optimization.",
      examples: ["Windows/Mac issues", "Software installation", "Performance optimization", "Error resolution"]
    },
    {
      icon: Shield,
      title: "Virus & Security",
      description: "Malware removal, security setup, and protection against future threats.",
      examples: ["Virus removal", "Antivirus setup", "Security audits", "Data protection"]
    },
    {
      icon: Wifi,
      title: "Network Setup",
      description: "Home and office network configuration, WiFi optimization, and connectivity issues.",
      examples: ["WiFi setup", "Router configuration", "Network troubleshooting", "Speed optimization"]
    },
    {
      icon: HardDrive,
      title: "Data Recovery",
      description: "Recovering lost files, backup solutions, and data migration services.",
      examples: ["File recovery", "Backup setup", "Data migration", "Cloud storage setup"]
    },
    {
      icon: Smartphone,
      title: "Device Integration",
      description: "Setting up and syncing smartphones, tablets, and other devices with your systems.",
      examples: ["Phone setup", "Email configuration", "App installation", "Device syncing"]
    }
  ];

  const webServices = [
    {
      icon: Globe,
      title: "Custom Websites",
      description: "Professional websites built specifically for your business needs and goals.",
      examples: ["Business websites", "Portfolio sites", "Landing pages", "Multi-page sites"]
    },
    {
      icon: Palette,
      title: "Website Design",
      description: "Modern, mobile-responsive designs that reflect your brand and engage visitors.",
      examples: ["Custom design", "Mobile optimization", "Brand integration", "User experience"]
    },
    {
      icon: Search,
      title: "SEO & Optimization",
      description: "Search engine optimization and website performance improvements.",
      examples: ["Google ranking", "Site speed", "Mobile-friendly", "Local SEO"]
    },
    {
      icon: ShoppingCart,
      title: "E-commerce Solutions",
      description: "Online stores and payment processing for businesses ready to sell online.",
      examples: ["Online stores", "Payment setup", "Product catalogs", "Order management"]
    },
    {
      icon: Settings,
      title: "Website Maintenance",
      description: "Ongoing updates, security, backups, and technical support for your website.",
      examples: ["Regular updates", "Security monitoring", "Content updates", "Technical support"]
    }
  ];

  const pricing = [
    {
      type: "IT Support",
      icon: Monitor,
      description: "Hourly rate for troubleshooting and technical support",
      price: "$75/hour",
      features: [
        "No minimum time requirement",
        "Same-day or next-day service",
        "Remote support when possible",
        "Follow-up support included"
      ]
    },
    {
      type: "Web Development",
      icon: Globe,
      description: "Project-based pricing for website development",
      price: "Starting at $1,500",
      features: [
        "Custom design included",
        "Mobile-responsive",
        "Basic SEO setup",
        "3 months of support"
      ]
    }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="hero-section py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            <span className="gradient-text">Professional Services</span> for Your Technology Needs
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            From daily IT support to custom web development, I provide comprehensive technology 
            solutions designed to help you and your business succeed.
          </p>
          <Link to="/contact">
            <Button size="lg" className="bg-accent hover:bg-accent-light text-accent-foreground text-lg px-8">
              Request a Quote
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      {/* IT Support Services */}
      <section className="py-20 bg-secondary/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">IT Support Services</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Day-to-Day IT Support
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Get reliable technical support for all your hardware, software, and networking needs. 
              No issue is too small or too complex.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {itServices.map((service, index) => (
              <Card key={index} className="service-card h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                      <service.icon className="w-5 h-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </div>
                  <CardDescription>{service.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {service.examples.map((example, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-trust flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{example}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Web Development Services */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">Web Development Services</Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Custom Web Solutions
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Professional websites and web applications that help your business grow and connect 
              with customers online.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {webServices.map((service, index) => (
              <Card key={index} className="service-card h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex items-center justify-center w-10 h-10 bg-accent/10 rounded-lg">
                      <service.icon className="w-5 h-5 text-accent" />
                    </div>
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                  </div>
                  <CardDescription>{service.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {service.examples.map((example, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-trust flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{example}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-secondary/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Transparent Pricing
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Fair, upfront pricing with no hidden fees. Get quality service that fits your budget.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {pricing.map((plan, index) => (
              <Card key={index} className="service-card">
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-primary-light rounded-2xl mx-auto mb-4">
                    <plan.icon className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <CardTitle className="text-2xl">{plan.type}</CardTitle>
                  <CardDescription className="text-base">{plan.description}</CardDescription>
                  <div className="text-3xl font-bold text-accent mt-4">{plan.price}</div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {plan.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-3">
                        <CheckCircle className="w-5 h-5 text-trust flex-shrink-0" />
                        <span className="text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>
                  <Link to="/contact" className="block mt-6">
                    <Button className="w-full bg-accent hover:bg-accent-light text-accent-foreground">
                      Get Started
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-16 text-center">
            <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>Quick Response Time</span>
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4" />
                <span>No Hidden Fees</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4" />
                <span>Satisfaction Guaranteed</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-primary-foreground/90 mb-8">
            Let's discuss your needs and find the perfect solution for your technology challenges.
          </p>
          <Link to="/contact">
            <Button size="lg" variant="secondary" className="text-lg px-8">
              Contact Me Today
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Services;